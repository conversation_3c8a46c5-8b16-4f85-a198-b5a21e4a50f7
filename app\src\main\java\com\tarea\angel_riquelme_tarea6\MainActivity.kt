package com.tarea.angel_riquelme_tarea6

import android.app.DatePickerDialog
import android.app.TimePickerDialog
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import java.text.SimpleDateFormat
import java.util.*

class MainActivity : AppCompatActivity() {

    private lateinit var edtRut: EditText
    private lateinit var edtNombre: EditText
    private lateinit var edtApellido: EditText
    private lateinit var edtDepartamento: EditText
    private lateinit var btnRegistrar: Button
    private lateinit var btnCompartir: Button
    private lateinit var recyclerVisitas: RecyclerView

    private lateinit var repository: VisitasRepository
    private lateinit var adapter: VisitasAdapter
    private val dateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault())

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        initViews()
        initRepository()
        setupRecyclerView()
        setupClickListeners()
        cargarVisitas()
    }

    private fun initViews() {
        edtRut = findViewById(R.id.edtRut)
        edtNombre = findViewById(R.id.edtNombre)
        edtApellido = findViewById(R.id.edtApellido)
        edtDepartamento = findViewById(R.id.edtDepartamento)
        btnRegistrar = findViewById(R.id.btnRegistrar)
        btnCompartir = findViewById(R.id.btnCompartir)
        recyclerVisitas = findViewById(R.id.recyclerVisitas)
    }

    private fun initRepository() {
        try {
            repository = VisitasRepository()
            Log.d("MainActivity", "Repository inicializado correctamente")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error al inicializar repository", e)
        }
    }

    private fun setupRecyclerView() {
        adapter = VisitasAdapter(emptyList()) { visita ->
            mostrarDialogoFechaSalida(visita)
        }
        recyclerVisitas.layoutManager = LinearLayoutManager(this)
        recyclerVisitas.adapter = adapter
    }

    private fun setupClickListeners() {
        btnRegistrar.setOnClickListener {
            registrarVisita()
        }

        btnCompartir.setOnClickListener {
            compartirDatos()
        }
    }

    private fun registrarVisita() {
        val rut = edtRut.text.toString().trim()
        val nombre = edtNombre.text.toString().trim()
        val apellido = edtApellido.text.toString().trim()
        val departamento = edtDepartamento.text.toString().trim()

        Log.d("MainActivity", "Intentando registrar visita: $nombre $apellido, RUT: $rut, Depto: $departamento")

        if (validarCampos(rut, nombre, apellido, departamento)) {
            Log.d("MainActivity", "Campos validados correctamente")
            val exito = repository.insertarVisita(rut, nombre, apellido, departamento)

            if (exito) {
                Log.d("MainActivity", "Visita registrada exitosamente")
                mostrarMensajeExito()
                limpiarCampos()
                cargarVisitas()
            } else {
                Log.e("MainActivity", "Error al registrar la visita")
                Toast.makeText(this, "Error al registrar la visita", Toast.LENGTH_SHORT).show()
            }
        } else {
            Log.w("MainActivity", "Validación de campos falló")
        }
    }

    private fun validarCampos(rut: String, nombre: String, apellido: String, departamento: String): Boolean {
        return when {
            rut.isEmpty() -> {
                edtRut.error = "El RUT es obligatorio"
                false
            }
            nombre.isEmpty() -> {
                edtNombre.error = "El nombre es obligatorio"
                false
            }
            apellido.isEmpty() -> {
                edtApellido.error = "El apellido es obligatorio"
                false
            }
            departamento.isEmpty() -> {
                edtDepartamento.error = "El departamento es obligatorio"
                false
            }
            else -> true
        }
    }

    private fun mostrarMensajeExito() {
        AlertDialog.Builder(this)
            .setTitle("Registro Exitoso")
            .setMessage("La visita se ha registrado correctamente")
            .setPositiveButton("OK", null)
            .show()
    }

    private fun limpiarCampos() {
        edtRut.text.clear()
        edtNombre.text.clear()
        edtApellido.text.clear()
        edtDepartamento.text.clear()
    }

    private fun cargarVisitas() {
        val visitas = repository.obtenerTodasLasVisitas()
        adapter.actualizarVisitas(visitas)
    }

    private fun mostrarDialogoFechaSalida(visita: VisitaModel) {
        val calendar = Calendar.getInstance()

        val datePickerDialog = DatePickerDialog(
            this,
            { _, year, month, dayOfMonth ->
                calendar.set(Calendar.YEAR, year)
                calendar.set(Calendar.MONTH, month)
                calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth)

                val timePickerDialog = TimePickerDialog(
                    this,
                    { _, hourOfDay, minute ->
                        calendar.set(Calendar.HOUR_OF_DAY, hourOfDay)
                        calendar.set(Calendar.MINUTE, minute)

                        val fechaSalida = calendar.time
                        if (fechaSalida.after(visita.fechaIngreso)) {
                            actualizarFechaSalida(visita, fechaSalida)
                        } else {
                            Toast.makeText(
                                this,
                                "La fecha de salida debe ser posterior al ingreso",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    },
                    calendar.get(Calendar.HOUR_OF_DAY),
                    calendar.get(Calendar.MINUTE),
                    true
                )
                timePickerDialog.show()
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        )

        datePickerDialog.show()
    }

    private fun actualizarFechaSalida(visita: VisitaModel, fechaSalida: Date) {
        val exito = repository.actualizarFechaSalida(visita.id, fechaSalida)

        if (exito) {
            Toast.makeText(this, "Fecha de salida actualizada", Toast.LENGTH_SHORT).show()
            cargarVisitas()
        } else {
            Toast.makeText(this, "Error al actualizar fecha de salida", Toast.LENGTH_SHORT).show()
        }
    }

    private fun compartirDatos() {
        val visitas = repository.obtenerTodasLasVisitas()

        if (visitas.isEmpty()) {
            Toast.makeText(this, "No hay visitas registradas", Toast.LENGTH_SHORT).show()
            return
        }

        val texto = StringBuilder("REGISTRO DE VISITAS\n\n")

        visitas.forEach { visita ->
            texto.append("Nombre: ${visita.nombre} ${visita.apellido}\n")
            texto.append("RUT: ${visita.rut}\n")
            texto.append("Departamento: ${visita.departamento}\n")
            texto.append("Ingreso: ${dateFormat.format(visita.fechaIngreso)}\n")

            if (visita.fechaSalida != null) {
                texto.append("Salida: ${dateFormat.format(visita.fechaSalida)}\n")
            } else {
                texto.append("Salida: Pendiente\n")
            }

            texto.append("-------------------\n")
        }

        val intent = Intent(Intent.ACTION_SEND).apply {
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, texto.toString())
            putExtra(Intent.EXTRA_SUBJECT, "Registro de Visitas")
        }

        startActivity(Intent.createChooser(intent, "Compartir registro de visitas"))
    }

    override fun onDestroy() {
        super.onDestroy()
        repository.close()
    }
}