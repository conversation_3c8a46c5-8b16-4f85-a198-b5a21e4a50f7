package com.tarea.angel_riquelme_tarea6

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import java.text.SimpleDateFormat
import java.util.*

class VisitasAdapter(
    private var visitas: List<VisitaModel>,
    private val onRegistrarSalida: (VisitaModel) -> Unit
) : RecyclerView.Adapter<VisitasAdapter.VisitaViewHolder>() {

    private val dateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault())

    class VisitaViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val txtNombre: TextView = view.findViewById(R.id.txtNombre)
        val txtRut: TextView = view.findViewById(R.id.txtRut)
        val txtDepartamento: TextView = view.findViewById(R.id.txtDepartamento)
        val txtFechaIngreso: TextView = view.findViewById(R.id.txtFechaIngreso)
        val txtFechaSalida: TextView = view.findViewById(R.id.txtFechaSalida)
        val btnRegistrarSalida: Button = view.findViewById(R.id.btnRegistrarSalida)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VisitaViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_visita, parent, false)
        return VisitaViewHolder(view)
    }

    override fun onBindViewHolder(holder: VisitaViewHolder, position: Int) {
        val visita = visitas[position]

        holder.txtNombre.text = "${visita.nombre} ${visita.apellido}"
        holder.txtRut.text = "RUT: ${visita.rut}"
        holder.txtDepartamento.text = "Depto: ${visita.departamento}"
        holder.txtFechaIngreso.text = "Ingreso: ${dateFormat.format(visita.fechaIngreso)}"

        if (visita.fechaSalida != null) {
            holder.txtFechaSalida.text = "Salida: ${dateFormat.format(visita.fechaSalida)}"
            holder.txtFechaSalida.visibility = View.VISIBLE
            holder.btnRegistrarSalida.visibility = View.GONE
        } else {
            holder.txtFechaSalida.visibility = View.GONE
            holder.btnRegistrarSalida.visibility = View.VISIBLE
            holder.btnRegistrarSalida.setOnClickListener {
                onRegistrarSalida(visita)
            }
        }
    }

    override fun getItemCount(): Int = visitas.size

    fun actualizarVisitas(nuevasVisitas: List<VisitaModel>) {
        this.visitas = nuevasVisitas
        notifyDataSetChanged()
    }
}