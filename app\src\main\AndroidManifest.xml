<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

<application
    android:name=".VisitasApplication"
    android:allowBackup="true"
    android:icon="@mipmap/ic_launcher"
    android:label="@string/app_name"
    android:theme="@style/Theme.Material3.DayNight">

    <activity
        android:name=".MainActivity"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
    </activity>

    <!-- Content Provider para compartir datos -->
    <provider
        android:name=".VisitasContentProvider"
        android:authorities="com.tuapp.visitas.provider"
        android:exported="true"
        android:readPermission="android.permission.READ_EXTERNAL_STORAGE"
        android:writePermission="android.permission.WRITE_EXTERNAL_STORAGE" />

</application>

</manifest>