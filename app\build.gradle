plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    id 'kotlin-kapt'
    id 'realm-android'
}

android {
    namespace 'com.tarea.angel_riquelme_tarea6'
    compileSdk 35

    defaultConfig {
        applicationId "com.tarea.angel_riquelme_tarea6"
        minSdk 28
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }
}

dependencies {
    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.material
    implementation libs.androidx.activity
    implementation libs.androidx.constraintlayout
    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core

    // RecyclerView
    implementation 'androidx.recyclerview:recyclerview:1.3.2'

    // Realm Database - Java version (más estable)
    implementation 'io.realm:realm-android-library:10.19.0'
    implementation 'io.realm:realm-android-kotlin-extensions:10.18.0'
}