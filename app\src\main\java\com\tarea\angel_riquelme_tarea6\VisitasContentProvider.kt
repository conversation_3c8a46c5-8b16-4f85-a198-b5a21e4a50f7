package com.tarea.angel_riquelme_tarea6

import android.content.*
import android.database.Cursor
import android.database.MatrixCursor
import android.net.Uri
import android.provider.BaseColumns
import java.text.SimpleDateFormat
import java.util.*

class VisitasContentProvider : ContentProvider() {

    companion object {
        const val AUTHORITY = "com.tuapp.visitas.provider"
        const val PATH_VISITAS = "visitas"

        val CONTENT_URI: Uri = Uri.parse("content://$AUTHORITY/$PATH_VISITAS")

        const val MATCH_VISITAS = 100
        const val MATCH_VISITA_ID = 101

        // Columnas de la tabla
        const val COLUMN_ID = BaseColumns._ID
        const val COLUMN_RUT = "rut"
        const val COLUMN_NOMBRE = "nombre"
        const val COLUMN_APELLIDO = "apellido"
        const val COLUMN_FECHA_INGRESO = "fecha_ingreso"
        const val COLUMN_DEPARTAMENTO = "departamento"
        const val COLUMN_FECHA_SALIDA = "fecha_salida"

        private val uriMatcher = UriMatcher(UriMatcher.NO_MATCH).apply {
            addURI(AUTHORITY, PATH_VISITAS, MATCH_VISITAS)
            addURI(AUTHORITY, "$PATH_VISITAS/#", MATCH_VISITA_ID)
        }
    }

    private lateinit var repository: VisitasRepository
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

    override fun onCreate(): Boolean {
        repository = VisitasRepository()
        return true
    }

    override fun query(
        uri: Uri,
        projection: Array<String>?,
        selection: String?,
        selectionArgs: Array<String>?,
        sortOrder: String?
    ): Cursor? {

        return when (uriMatcher.match(uri)) {
            MATCH_VISITAS -> {
                val visitas = repository.obtenerTodasLasVisitas()
                val cursor = MatrixCursor(arrayOf(
                    COLUMN_ID,
                    COLUMN_RUT,
                    COLUMN_NOMBRE,
                    COLUMN_APELLIDO,
                    COLUMN_FECHA_INGRESO,
                    COLUMN_DEPARTAMENTO,
                    COLUMN_FECHA_SALIDA
                ))

                visitas.forEachIndexed { index, visita ->
                    cursor.addRow(arrayOf(
                        index.toLong(),
                        visita.rut,
                        visita.nombre,
                        visita.apellido,
                        dateFormat.format(visita.fechaIngreso),
                        visita.departamento,
                        visita.fechaSalida?.let { dateFormat.format(it) } ?: ""
                    ))
                }

                cursor
            }
            else -> null
        }
    }

    override fun getType(uri: Uri): String? {
        return when (uriMatcher.match(uri)) {
            MATCH_VISITAS -> "vnd.android.cursor.dir/vnd.$AUTHORITY.$PATH_VISITAS"
            MATCH_VISITA_ID -> "vnd.android.cursor.item/vnd.$AUTHORITY.$PATH_VISITAS"
            else -> null
        }
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        return when (uriMatcher.match(uri)) {
            MATCH_VISITAS -> {
                values?.let { cv ->
                    val rut = cv.getAsString(COLUMN_RUT) ?: ""
                    val nombre = cv.getAsString(COLUMN_NOMBRE) ?: ""
                    val apellido = cv.getAsString(COLUMN_APELLIDO) ?: ""
                    val departamento = cv.getAsString(COLUMN_DEPARTAMENTO) ?: ""

                    if (repository.insertarVisita(rut, nombre, apellido, departamento)) {
                        context?.contentResolver?.notifyChange(uri, null)
                        Uri.withAppendedPath(CONTENT_URI, "1")
                    } else {
                        null
                    }
                }
            }
            else -> null
        }
    }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<String>?): Int {
        return 0 // No implementamos eliminación por seguridad
    }

    override fun update(
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<String>?
    ): Int {
        return when (uriMatcher.match(uri)) {
            MATCH_VISITAS -> {
                values?.let { cv ->
                    val visitaId = cv.getAsString("visita_id") ?: ""
                    val fechaSalidaStr = cv.getAsString(COLUMN_FECHA_SALIDA)

                    if (visitaId.isNotEmpty() && fechaSalidaStr != null) {
                        try {
                            val fechaSalida = dateFormat.parse(fechaSalidaStr)
                            if (repository.actualizarFechaSalida(visitaId, fechaSalida)) {
                                context?.contentResolver?.notifyChange(uri, null)
                                1
                            } else {
                                0
                            }
                        } catch (e: Exception) {
                            0
                        }
                    } else {
                        0
                    }
                }
            }
            else -> 0
        } ?: 0
    }
}